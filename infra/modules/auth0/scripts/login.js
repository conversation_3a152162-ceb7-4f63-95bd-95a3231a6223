function login(email, password, callback) {

  var pushUnique = function (array, value){
    if(Array.isArray(value)){
      value.forEach(function(entry) {
        pushUnique(array, entry);
      });
    } else {
     if(array.indexOf(value) === -1){
       array.push(value);
     }
    }
  };

  //this script uses the "tedious" library
  //more info here: http://pekim.github.io/tedious/index.html
  var connection = sqlserver.connect({
      userName: 'autho_readonly',
      password: '@uth0_re@d0nly',
      server: '***********',
      options: {
          port: 63207,
          database: 'qvnz_test',
          rowCollectionOnRequestCompletion: true
      }
  });
  var query = "SELECT TA_USER_ID, TA_USER_NAME, TA_USER.EMAIL_ADDRESS, FIRST_NAME, " +
    "\nLAST_NAME, TA_USER.ACCOUNT_DISABLED USER_STATUS, TA_USER.UPDATE_DATA, "+
    "\nQV_CUSTOM_ACCESS.URL, TA_GROUP.ACCOUNT_DISABLED GROUP_STATUS, TA_GROUP.GROUP_TYPE, "+
    "\nRATING_AUTHORITY.CODE RATING_AUTHORITY_CODE, RATING_AUTHORITY.ORGANISATION_SHORT_NAME, TA_GROUP_NAME "+
    "\nFROM TA_USER INNER JOIN QV_USER ON TA_USER_NAME = QV_USER "+
    "\nINNER JOIN TA_GROUP ON TA_USER.TA_GROUP_ID = TA_GROUP.TA_GROUP_ID "+
    "\nLEFT JOIN QV_CUSTOM_ACCESS ON  (TA_GROUP.RATING_AUTHORITY_ID = QV_CUSTOM_ACCESS.RATING_AUTHORITY_ID OR TA_GROUP.REGION_ID = QV_CUSTOM_ACCESS.REGION_ID ) "+
    "\nLEFT JOIN RATING_AUTHORITY ON TA_GROUP.RATING_AUTHORITY_ID = RATING_AUTHORITY.RATING_AUTHORITY_ID "+
    "\nWHERE QV_USER NOT LIKE 'QVNZ-%' "+
    "AND (TA_USER_NAME = @email OR TA_USER.EMAIL_ADDRESS = @email) and PASSWORD = @password";

  connection.on('debug', function (text) {
  }).on('errorMessage', function (text) {
    console.log(JSON.stringify(text, null, 2));
  }).on('infoMessage', function (text) {
     //console.log(JSON.stringify(text, null, 2));
  });

  connection.on('connect', function (err) {
    if (err) return callback(err);

    var request = new sqlserver.Request(query, function (err, rowCount, rows) {

      var userStatus = (rowCount >= 1) ? rows[0][5].value:"";
      var groupStatus = (rowCount >= 1) ? rows[0][8].value:"";

      if (err) {
        callback(new Error(err));
      } else if (rowCount < 1) {
         console.log('******* User not found ******** ');
        callback(new WrongUsernameOrPasswordError(email));
      } else if(rowCount >= 1 && (userStatus || groupStatus)){ // Checking User or Group Active or not
        callback(new Error("User or Group is deactivated "));
      } else {
        query = "SELECT RATING_AUTHORITY.CODE RATING_AUTHORITY_CODE, "+
          "REGION_TYPE.CODE REGION_CODE, ALLOW_MAP, VIEW_SRA, UPDATE_DATA, UPDATE_RC_RID, UPDATE_TA_RID "+
          "\nFROM TA_USER " +
          "\nINNER JOIN TA_GROUP ON TA_USER.TA_GROUP_ID = TA_GROUP.TA_GROUP_ID " +
          "\nINNER JOIN UPDATE_ACCESS ON TA_GROUP.TA_GROUP_ID = UPDATE_ACCESS.TA_GROUP_ID "+
          "\nINNER JOIN RATING_AUTHORITY ON UPDATE_ACCESS.RATING_AUTHORITY_ID= RATING_AUTHORITY.RATING_AUTHORITY_ID "+
          "\nINNER JOIN REGION_TYPE ON UPDATE_ACCESS.REGION_ID = REGION_TYPE.REGION_ID "+
          "\nWHERE TA_USER.TA_USER_ID = @id";
        var request2 = new sqlserver.Request(query, function (err, rowCount2, rows2) {
          if (err) {
            callback(new Error(err));
          } else {
            var updateDataAccess = rows[0][6].value; // TA Read/Write
            var role = updateDataAccess?'EXTERNAL_USER_RW':'EXTERNAL_USER_READ';
            // Populating External QIVS URL for logged in user
            // If if is null assign default URL
            var externalQIVSURL = (rows[0][7].value !== null) ? (rows[0][7].value) : (configuration.EXTERNAL_QIVS_URL);

            var userDetails = {
              "user_id": rows[0][0].value,
              "nickname": rows[0][1].value,
              "email": rows[0][2].value,
              "email_verified": true,
              "name": rows[0][3].value+" "+rows[0][4].value,
              "userPrincipalName": rows[0][2].value,
              "given_name": rows[0][3].value,
              "family_name": rows[0][4].value,
              "external_qivs_url": externalQIVSURL,
              "groupType": rows[0][9].value,
              "userTACode": rows[0][10].value,
              "userTAName": rows[0][11].value,
              "taGroupName": "FAKEOVG",
              "identities": [{
                "user_id":rows[0][0].value,
                "connection": "${env}-QIVDB",
                "provider": "QIVS",
                "isSocial": false
              }],
              "app_metadata": {},
              "roles": [role],
              "permissions": {
                "TerritorialAuthority": {},
                "RegionalCouncil": {}
              },
            };

            rows2.forEach(function(row) {
              var idx = 0;
              var ta = row[idx++].value;
              var rc = row[idx++].value;
              var allowMap = row[idx++].value !== 0;
              var viewSRA = row[idx++].value !== 0;
              var updateData = row[idx++].value !== 0; // TA Read/Write
              var updateRcRid = row[idx++].value !== 0; // ignored for now
              var updateTaRid = row[idx++].value !== 0; // ignored for now
              // TODO read the row and update claims and permissions.

              var permissions= [];
              if(allowMap){
                permissions.push("Monarch:Property:ViewMap",
                                 "Monarch:Property:Search");
              }
              if(viewSRA){
                permissions.push("Monarch:Property:Show");
              }
              if(updateData){
                permissions.push("Monarch:Property:Edit");
              }

              var taPermissionContainer = userDetails.permissions.TerritorialAuthority[ta];
              if(typeof taPermissionContainer === "undefined"){
                taPermissionContainer = permissions;
                userDetails.permissions.TerritorialAuthority[ta]= taPermissionContainer;
              } else {
                pushUnique(taPermissionContainer, permissions );
              }

              var rcPermissionContainer = userDetails.permissions.RegionalCouncil[rc];
              if(typeof rcPermissionContainer === "undefined"){
                rcPermissionContainer = permissions;
                userDetails.permissions.RegionalCouncil[rc]= rcPermissionContainer;
              } else {
                pushUnique(rcPermissionContainer, permissions);
              }

            });
            callback(null, userDetails);
          }
        });
        request2.addParameter('id', sqlserver.Types.Int, + rows[0][0].value);
        connection.execSql(request2);
      }
    }
    );

    var crypto = require('crypto');
    var shasum = crypto.createHash('sha1');
    shasum.update(new Buffer(password, 'utf16le'));
    var hash = shasum.digest('base64');
    console.log("password "+hash);
    request.addParameter('email', sqlserver.Types.VarChar, email);
    request.addParameter('password', sqlserver.Types.VarChar, hash);
    connection.execSql(request);
  });
}
