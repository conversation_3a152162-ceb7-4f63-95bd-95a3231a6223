# Terraform configuration
terraform {
  required_providers {
    auth0 = {
      source  = "auth0/auth0"
      version = ">= 1.0.0"
    }
  }
}

# provider "aws" {
#     region = "ap-southeast-2"
#     assume_role {
#         role_arn = var.target_account_role_arn
#     }
# }

# Auth0 Provider for Development Environment
provider "auth0" {
  alias         = "dev"
  domain        = var.auth0_domains.dev
  client_id     = var.auth0_client_credentials.dev.client_id
  client_secret = var.auth0_client_credentials.dev.client_secret
  debug         = false
}

# Auth0 Provider for Test Environment
provider "auth0" {
  alias         = "test"
  domain        = var.auth0_domains.test
  client_id     = var.auth0_client_credentials.test.client_id
  client_secret = var.auth0_client_credentials.test.client_secret
  debug         = false
}

# Auth0 Provider for Production Environment
provider "auth0" {
  alias         = "prod"
  domain        = var.auth0_domains.prod
  client_id     = var.auth0_client_credentials.prod.client_id
  client_secret = var.auth0_client_credentials.prod.client_secret
  debug         = false
}
