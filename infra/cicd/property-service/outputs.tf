# Development Environment Outputs
output "dev_monarch_web_client_id" {
  description = "The client ID of the Monarch Web client for dev environment"
  value       = module.dev_auth0.monarch_web_client_id
}

output "dev_monarch_web_client_secret" {
  description = "The client secret of the Monarch Web client for dev environment"
  value       = module.dev_auth0.monarch_web_client_secret
  sensitive   = true
}

output "dev_qivdb_connection_id" {
  description = "The ID of the QIVDB connection for dev environment"
  value       = module.dev_auth0.qivdb_connection_id
}

# Test Environment Outputs
output "test_monarch_web_client_id" {
  description = "The client ID of the Monarch Web client for test environment"
  value       = module.test_auth0.monarch_web_client_id
}

output "test_monarch_web_client_secret" {
  description = "The client secret of the Monarch Web client for test environment"
  value       = module.test_auth0.monarch_web_client_secret
  sensitive   = true
}

output "test_qivdb_connection_id" {
  description = "The ID of the QIVDB connection for test environment"
  value       = module.test_auth0.qivdb_connection_id
}

# Production Environment Outputs
output "prod_monarch_web_client_id" {
  description = "The client ID of the Monarch Web client for prod environment"
  value       = module.prod_auth0.monarch_web_client_id
}

output "prod_monarch_web_client_secret" {
  description = "The client secret of the Monarch Web client for prod environment"
  value       = module.prod_auth0.monarch_web_client_secret
  sensitive   = true
}

output "prod_qivdb_connection_id" {
  description = "The ID of the QIVDB connection for prod environment"
  value       = module.prod_auth0.qivdb_connection_id
}
