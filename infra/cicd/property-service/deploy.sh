#!/bin/bash

# Auth0 Multi-Tenant Deployment Script
# This script helps deploy Auth0 configurations across multiple environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install Terraform first."
        exit 1
    fi
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI first."
        exit 1
    fi
    
    print_status "Prerequisites check passed."
}

# Function to validate environment
validate_environment() {
    local env=$1
    
    if [[ ! "$env" =~ ^(dev|test|prod)$ ]]; then
        print_error "Invalid environment: $env. Must be one of: dev, test, prod"
        exit 1
    fi
}

# Function to initialize Terraform
init_terraform() {
    print_status "Initializing Terraform..."
    terraform init
}

# Function to plan deployment
plan_deployment() {
    local env=$1
    print_status "Planning deployment for $env environment..."
    terraform plan -var-file="terraform.tfvars" -target="module.${env}_auth0"
}

# Function to apply deployment
apply_deployment() {
    local env=$1
    print_status "Applying deployment for $env environment..."
    terraform apply -var-file="terraform.tfvars" -target="module.${env}_auth0" -auto-approve
}

# Function to validate deployment
validate_deployment() {
    local env=$1
    print_status "Validating deployment for $env environment..."
    terraform validate
}

# Function to show outputs
show_outputs() {
    local env=$1
    print_status "Showing outputs for $env environment..."
    terraform output | grep "${env}_"
}

# Function to deploy specific environment
deploy_environment() {
    local env=$1
    
    validate_environment "$env"
    
    print_status "Deploying Auth0 configuration for $env environment..."
    
    # Check if terraform.tfvars exists
    if [[ ! -f "terraform.tfvars" ]]; then
        print_warning "terraform.tfvars not found. Please copy from terraform.tfvars.example and configure."
        exit 1
    fi
    
    # Initialize if needed
    if [[ ! -d ".terraform" ]]; then
        init_terraform
    fi
    
    # Validate configuration
    validate_deployment "$env"
    
    # Plan deployment
    plan_deployment "$env"
    
    # Ask for confirmation
    read -p "Do you want to apply these changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        apply_deployment "$env"
        show_outputs "$env"
        print_status "Deployment completed successfully for $env environment!"
    else
        print_status "Deployment cancelled."
    fi
}

# Function to deploy all environments
deploy_all() {
    print_status "Deploying Auth0 configuration for all environments..."
    
    for env in dev test prod; do
        print_status "Processing $env environment..."
        deploy_environment "$env"
        echo
    done
    
    print_status "All environments deployed successfully!"
}

# Function to destroy environment
destroy_environment() {
    local env=$1
    
    validate_environment "$env"
    
    print_warning "This will DESTROY the Auth0 configuration for $env environment!"
    read -p "Are you sure you want to continue? Type 'yes' to confirm: " -r
    echo
    
    if [[ $REPLY == "yes" ]]; then
        print_status "Destroying $env environment..."
        terraform destroy -var-file="terraform.tfvars" -target="module.${env}_auth0" -auto-approve
        print_status "Environment $env destroyed successfully!"
    else
        print_status "Destruction cancelled."
    fi
}

# Function to show help
show_help() {
    echo "Auth0 Multi-Tenant Deployment Script"
    echo
    echo "Usage: $0 [COMMAND] [ENVIRONMENT]"
    echo
    echo "Commands:"
    echo "  deploy <env>    Deploy Auth0 configuration for specific environment (dev|test|prod)"
    echo "  deploy-all      Deploy Auth0 configuration for all environments"
    echo "  plan <env>      Show deployment plan for specific environment"
    echo "  destroy <env>   Destroy Auth0 configuration for specific environment"
    echo "  init            Initialize Terraform"
    echo "  validate        Validate Terraform configuration"
    echo "  outputs <env>   Show outputs for specific environment"
    echo "  help            Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy dev"
    echo "  $0 deploy-all"
    echo "  $0 plan prod"
    echo "  $0 outputs test"
}

# Main script logic
main() {
    check_prerequisites
    
    case "${1:-help}" in
        "deploy")
            if [[ -z "$2" ]]; then
                print_error "Environment required for deploy command"
                show_help
                exit 1
            fi
            deploy_environment "$2"
            ;;
        "deploy-all")
            deploy_all
            ;;
        "plan")
            if [[ -z "$2" ]]; then
                print_error "Environment required for plan command"
                show_help
                exit 1
            fi
            validate_environment "$2"
            init_terraform
            plan_deployment "$2"
            ;;
        "destroy")
            if [[ -z "$2" ]]; then
                print_error "Environment required for destroy command"
                show_help
                exit 1
            fi
            destroy_environment "$2"
            ;;
        "init")
            init_terraform
            ;;
        "validate")
            validate_deployment
            ;;
        "outputs")
            if [[ -z "$2" ]]; then
                print_error "Environment required for outputs command"
                show_help
                exit 1
            fi
            validate_environment "$2"
            show_outputs "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
