# Auth0 Domain Configuration
variable "auth0_domains" {
  description = "Auth0 domains for each environment"
  type = object({
    dev  = string
    test = string
    prod = string
  })
  default = {
    dev  = "qvmonarch-dev.au.auth0.com"
    test = "qvmonarch-test.au.auth0.com"
    prod = "qvmonarch.au.auth0.com"
  }
}

# Monarch URL Configuration
variable "monarch_urls" {
  description = "Monarch URLs for each environment"
  type = object({
    dev  = string
    test = string
    prod = string
  })
  default = {
    dev  = "https://dev.qvmonarch.co.nz"
    test = "https://test.qvmonarch.co.nz"
    prod = "https://qvmonarch.co.nz"
  }
}

# Database Credentials Configuration
variable "db_credentials" {
  description = "Database credentials for each environment"
  type = object({
    dev = object({
      password = string
      username = string
      host     = string
      port     = number
    })
    test = object({
      password = string
      username = string
      host     = string
      port     = number
    })
    prod = object({
      password = string
      username = string
      host     = string
      port     = number
    })
  })
  sensitive = true
}

# External QIVS URL Configuration
variable "external_qivs_urls" {
  description = "External QIVS URLs for each environment"
  type = object({
    dev  = string
    test = string
    prod = string
  })
  default = {
    dev  = "qvnz_dev"
    test = "qvnz_test"
    prod = "qvnz_prod"
  }
}

# Auth0 Client Credentials for each environment
variable "auth0_client_credentials" {
  description = "Auth0 client credentials for each environment"
  type = object({
    dev = object({
      client_id     = string
      client_secret = string
    })
    test = object({
      client_id     = string
      client_secret = string
    })
    prod = object({
      client_id     = string
      client_secret = string
    })
  })
  sensitive = true
}
