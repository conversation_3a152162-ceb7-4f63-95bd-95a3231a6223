# Auth0 Domain Configuration
auth0_domains = {
  dev  = "qvmonarch-dev.au.auth0.com"
  test = "qvmonarch-test.au.auth0.com"
  prod = "qvmonarch.au.auth0.com"
}

# Monarch URL Configuration
monarch_urls = {
  dev  = "https://dev.qvmonarch.co.nz"
  test = "https://test.qvmonarch.co.nz"
  prod = "https://qvmonarch.co.nz"
}

# Database Credentials Configuration
# Note: In production, use environment variables or secure secret management
db_credentials = {
  dev = {
    password = "dev_password"
    username = "dev_username"
    host     = "dev.db.host"
    port     = 5432
  }
  test = {
    password = "test_password"
    username = "test_username"
    host     = "test.db.host"
    port     = 5432
  }
  prod = {
    password = "prod_password"
    username = "prod_username"
    host     = "prod.db.host"
    port     = 5432
  }
}

# External QIVS URL Configuration
external_qivs_urls = {
  dev  = "qvnz_dev"
  test = "qvnz_test"
  prod = "qvnz_prod"
}

# Auth0 Client Credentials
# Note: In production, use environment variables or secure secret management
auth0_client_credentials = {
  dev = {
    client_id     = "your_dev_client_id"
    client_secret = "your_dev_client_secret"
  }
  test = {
    client_id     = "your_test_client_id"
    client_secret = "your_test_client_secret"
  }
  prod = {
    client_id     = "your_prod_client_id"
    client_secret = "your_prod_client_secret"
  }
}
