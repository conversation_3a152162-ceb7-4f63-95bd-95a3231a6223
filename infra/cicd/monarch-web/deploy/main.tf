locals {
  service = {
    name = "monarch-web"
    terraform_enabled = true
  }

  environments = [
    "dev",
    "test",
    "uat",
    "prod"
  ]

  # Environment-specific tfvars configurations
  tfvars_by_env = {
    dev = <<EOF
env                 = "sessionmgr"
monarch_url         = "https://dev.qvmonarch.co.nz/"
auth0_domain        = "qvmonarch-dev.au.auth0.com"
auth0_client_id     = "2RlhQMtk1BjPozKaLkeBE2eRlyJOuXqF"
auth0_client_secret = "****************************************************************"

db_password      = "autho_readonly"
db_user_name     = "@uth0_re@d0nly"
db_server_host   = "***********"
db_server_port   = 63207
external_qivs_url = "qvnz_dev"
EOF
    test = <<EOF
env                 = "sessionmgr"
monarch_url         = "https://test.qvmonarch.co.nz/"
auth0_domain        = "qvmonarch-test.au.auth0.com"
auth0_client_id     = "2RlhQMtk1BjPozKaLkeBE2eRlyJOuXqF"
auth0_client_secret = "****************************************************************"

db_password      = "autho_readonly"
db_user_name     = "@uth0_re@d0nly"
db_server_host   = "***********"
db_server_port   = 63207
external_qivs_url = "qvnz_test"
EOF
    uat = <<EOF
env                 = "sessionmgr"
monarch_url         = "https://uat.qvmonarch.co.nz/"
auth0_domain        = "qvmonarch-uat.au.auth0.com"
auth0_client_id     = "2RlhQMtk1BjPozKaLkeBE2eRlyJOuXqF"
auth0_client_secret = "****************************************************************"

db_password      = "autho_readonly"
db_user_name     = "@uth0_re@d0nly"
db_server_host   = "***********"
db_server_port   = 63207
external_qivs_url = "qvnz_uat"
EOF
    prod = <<EOF
env                 = "sessionmgr"
monarch_url         = "https://qvmonarch.co.nz/"
auth0_domain        = "qvmonarch.au.auth0.com"
auth0_client_id     = "2RlhQMtk1BjPozKaLkeBE2eRlyJOuXqF"
auth0_client_secret = "****************************************************************"

db_password      = "autho_readonly"
db_user_name     = "@uth0_re@d0nly"
db_server_host   = "***********"
db_server_port   = 63207
external_qivs_url = "qvnz_prod"
EOF
  }
}

module "service" {
  for_each = {
    for env in local.environments : env => {
      service_name      = local.service.name
      env               = env
      terraform_enabled = local.service.terraform_enabled
    }
  }

  source = "../../../modules/k8s-deploy"

  app_name          = each.value.service_name
  app_env           = each.value.env
  terraform_enabled = each.value.terraform_enabled
}

module "ssm_tfvars" {
  for_each = {
    for env in concat(local.environments, ["preprod"]) : env => {
      service_name     = local.service.name
      env              = env
      additional_value = lookup(local.tfvars_by_env, env, "")
    }
  }

  source   = "../../../modules/ssm_tfvars"
  app_name = each.value.service_name
  app_env  = each.value.env
  additional_value = each.value.additional_value
}
