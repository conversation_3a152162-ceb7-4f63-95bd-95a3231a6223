locals {
  services = [
    { name = "classification", terraform_enabled = true },
    { name = "home-valuation", terraform_enabled = true },
    { name = "media", terraform_enabled = true },
    { name = "monarch-web", terraform_enabled = true },
    { name = "property", terraform_enabled = true },
    { name = "reporting", terraform_enabled = true },
    { name = "ereporting", terraform_enabled = true },
    { name = "roll-maintenance", terraform_enabled = true },
    { name = "sale", terraform_enabled = true },
    { name = "sale-analysis", terraform_enabled = true },
    { name = "user-profile", terraform_enabled = true },
  ]

  environments = [
    "dev",
    "test",
    "uat",
    "prod"
  ]
}

module "service" {
  # for each service and each environment, create a module instance
  for_each = {
    for item in flatten([
      for service in local.services : [
        for env in local.environments : {
          key          = "${service.name}-${env}"
          service_name = service.name
          terraform_enabled = lookup(service, "terraform_enabled", false)
          env          = env
        }
      ]
    ])
    : item.key => item
  }

  source            = "../../../modules/k8s-deploy"
  app_name          = each.value.service_name
  app_env           = each.value.env
  terraform_enabled = each.value.terraform_enabled
}

module "ssm_tfvars" {
  for_each = {
    for item in flatten([
      for service in local.services : [
        for env in concat(local.environments, ["preprod"]) : {
          key          = "${service.name}-${env}"
          service_name = service.name
          env          = env
        }
      ]
    ])
    : item.key => item
  }

  source   = "../../../modules/ssm_tfvars"
  app_name = each.value.service_name
  app_env  = each.value.env
}